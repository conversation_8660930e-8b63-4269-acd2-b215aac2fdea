import re
import collections
import pandas as pd
import time
import threading
import random
import logging
from datetime import datetime, timedelta
import openai
import concurrent.futures

# --- Logging ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# --- Rate Limiter for 200 RPM ---
class RateLimiter:
    def __init__(self, max_requests_per_minute=200):
        self.max_requests = max_requests_per_minute
        self.requests = []
        self.lock = threading.Lock()

    def wait_if_needed(self):
        with self.lock:
            now = datetime.now()
            cutoff = now - timedelta(minutes=1)
            self.requests = [t for t in self.requests if t > cutoff]
            if len(self.requests) >= self.max_requests:
                wait = (self.requests[0] + timedelta(minutes=1) - now).total_seconds()
                logging.info(f"Rate limit hit, sleeping {wait:.1f}s")
                time.sleep(wait)
            self.requests.append(datetime.now())

rate_limiter = RateLimiter()

# --- OpenAI SDK Setup ---
openai.api_key = "sk-crR1w2kZ5YLwiRsZwh1VbmPYlcRWo3Pmlv8pw6Eryau6ZXk1"
openai.base_url = "https://api.opentyphoon.ai/v1"

# --- Prompt Setup ---
PROMPT_SYS_BASE = (
    "You are an expert financial analyst with deep market knowledge. Your task is to predict stock price movements based on financial indicators and market conditions.\n\n"
    "INSTRUCTIONS:\n"
    "1. Analyze the given financial scenario step-by-step\n"
    "2. Consider fundamental analysis principles (P/E ratios, earnings, debt levels, profitability metrics)\n"
    "3. Think about market psychology and investor behavior\n"
    "4. Provide your reasoning in a 'Reasoning:' section with clear bullet points\n"
    "5. End with exactly one line: 'FINAL ANSWER: [A/B/C/D/Rise/Fall]'\n"
    "6. Be decisive - choose the most likely outcome based on financial theory\n\n"
    "FINANCIAL PRINCIPLES TO CONSIDER:\n"
    "- Higher P/E ratios generally indicate higher valuations (bullish if justified)\n"
    "- Earnings growth typically drives price appreciation\n"
    "- High debt-to-equity ratios increase financial risk (bearish)\n"
    "- Improving ROE/ROA signals better management efficiency (bullish)\n"
    "- Dividend increases attract income investors (often bullish)\n"
    "- Revenue growth is fundamental to long-term price appreciation\n\n"
)

# Enhanced few-shot examples with more diverse scenarios
FEW_SHOT = (
    "Example 1:\n"
    "Question: Company's P/E ratio increases from 12 to 18 while earnings grow 15%. What's the likely price direction?\n"
    "Reasoning:\n"
    "• P/E expansion from 12 to 18 shows investors willing to pay more per dollar of earnings\n"
    "• 15% earnings growth provides fundamental support for higher valuation\n"
    "• Combined effect: both multiple expansion and earnings growth drive price up\n"
    "• Market sentiment appears positive based on P/E expansion\n"
    "FINAL ANSWER: Rise\n\n"

    "Example 2:\n"
    "Question: Debt-to-equity ratio jumps from 0.3 to 1.8, but revenue grows 25%. Stock outlook?\n"
    "Reasoning:\n"
    "• Massive increase in leverage (0.3 to 1.8) significantly raises financial risk\n"
    "• 25% revenue growth is positive but may not offset leverage concerns\n"
    "• High debt levels limit financial flexibility and increase bankruptcy risk\n"
    "• Risk-averse investors likely to sell despite revenue growth\n"
    "FINAL ANSWER: Fall\n\n"

    "Example 3:\n"
    "Question: ROE improves from 8% to 15%, margins expand, but industry faces headwinds. Price direction?\n"
    "Reasoning:\n"
    "• ROE improvement from 8% to 15% shows excellent operational efficiency gains\n"
    "• Expanding margins indicate strong pricing power or cost control\n"
    "• Company outperforming industry suggests competitive advantages\n"
    "• Strong fundamentals often overcome short-term industry challenges\n"
    "FINAL ANSWER: Rise\n\n"

    "Example 4:\n"
    "Question: Dividend yield increases to 6% but payout ratio reaches 95%. Sustainability concerns?\n"
    "Reasoning:\n"
    "• 6% dividend yield is attractive to income investors\n"
    "• 95% payout ratio leaves little room for dividend growth or reinvestment\n"
    "• High payout ratio signals potential dividend cut risk\n"
    "• Unsustainable dividends often lead to sharp price declines when cut\n"
    "FINAL ANSWER: Fall\n\n"

    "Example 5:\n"
    "Question: Company reports 30% earnings decline but announces major cost restructuring. Market reaction?\n"
    "Reasoning:\n"
    "• 30% earnings decline is significantly negative for current valuation\n"
    "• Cost restructuring suggests management taking action to improve efficiency\n"
    "• Near-term pain from restructuring vs. long-term benefits creates uncertainty\n"
    "• Immediate market reaction typically focuses on current earnings miss\n"
    "FINAL ANSWER: Fall"
)

# --- Answer Extractor ---
class ChoiceExtractor:
    final_pattern = re.compile(r"(?:FINAL\s*ANSWER)\s*[:\-]\s*([ABCD]|Rise|Fall)", re.IGNORECASE)
    last_pattern = re.compile(r"\b([ABCD]|Rise|Fall)\b(?!.*\b(?:[ABCD]|Rise|Fall)\b)", re.IGNORECASE)
    conf_pattern = re.compile(r"confidence\s*[:=]\s*(high|medium|low)", re.IGNORECASE)

    def extract(self, text: str):
        t = text.strip()
        m = self.final_pattern.search(t)
        choice = m.group(1) if m else None
        if not choice:
            m2 = self.last_pattern.search(t)
            choice = m2.group(1) if m2 else None
        if choice:
            choice = choice.capitalize() if choice.lower() in ['rise', 'fall'] else choice.upper()
        conf = 0.6
        m3 = self.conf_pattern.search(t)
        if m3:
            conf = {'high': 0.9, 'medium': 0.7, 'low': 0.4}.get(m3.group(1).lower(), 0.6)
        if any(k in t.lower() for k in ['clearly', 'obviously', 'definitely']):
            conf = min(1.0, conf + 0.1)
        return choice, conf

extractor = ChoiceExtractor()

def select_temps(query):
    length = len(query)
    keywords = ['ratio', 'earnings', 'revenue', 'debt', 'margin', 'dividend']
    count = sum(1 for k in keywords if k in query.lower())
    if length < 60 and count <= 1:
        return [0.2]
    elif length < 120 and count <= 2:
        return [0.3, 0.5]
    return [0.4, 0.6]

def override_heuristic(result, query):
    bullish = ['rise', 'increase', 'up', 'growth', 'improve', 'expand', 'strong', 'positive']
    bearish = ['fall', 'decline', 'down', 'drop', 'weak', 'negative', 'risk', 'concern']
    bull_count = sum(1 for w in bullish if w in query.lower())
    bear_count = sum(1 for w in bearish if w in query.lower())
    if bull_count > bear_count + 1 and result == 'Fall':
        return 'Rise'
    if bear_count > bull_count + 1 and result == 'Rise':
        return 'Fall'
    return result

# --- Prediction Class ---
class TyphoonPredictor:
    def __init__(self, limiter):
        self.limiter = limiter

    def predict(self, full_prompt, query):
        temps = select_temps(query)
        votes = collections.defaultdict(float)
        responses = []

        for t in temps:
            try:
                self.limiter.wait_if_needed()
                response = openai.chat.completions.create(
                    model="typhoon-v2.1-12b-instruct",
                    messages=[
                        {"role": "system", "content": full_prompt},
                        {"role": "user", "content": query}
                    ],
                    temperature=t,
                    max_tokens=400,
                    top_p=0.9
                )
                text = response.choices[0].message.content
                responses.append(text)
                logging.info(f"RAW @ t={t}: {text[:200]}")
                choice, conf = extractor.extract(text)
                if choice:
                    votes[choice] += conf
                    logging.info(f"Vote: {choice} (conf: {conf}) at temp {t}")
            except Exception as e:
                logging.error(f"API call failed at temp {t}: {e}")
                continue

        if not votes:
            return None
        scores = {c: v / sum(votes.values()) for c, v in votes.items()}
        result = max(scores, key=scores.get)
        return override_heuristic(result, query)

# --- Fallback Logic ---
LABELS = ['A', 'B', 'C', 'D', 'Rise', 'Fall']
def fallback(query):
    q = query.lower()
    if any(w in q for w in ['earnings growth', 'revenue increase', 'profit up', 'roi improve']):
        return 'Rise'
    if any(w in q for w in ['debt increase', 'loss', 'decline', 'risk']):
        return 'Fall'
    return random.choice(LABELS)

# --- Main Runner ---
def run(test_df):
    predictor = TyphoonPredictor(rate_limiter)
    full_prompt = PROMPT_SYS_BASE + FEW_SHOT
    results = [None] * len(test_df)

    def task(i, q):
        try:
            res = predictor.predict(full_prompt, q)
            return i, res or fallback(q)
        except Exception as e:
            logging.error(f"Task {i} failed: {e}")
            return i, fallback(q)

    with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
        futures = [executor.submit(task, idx, row['query']) for idx, row in test_df.iterrows()]
        for f in concurrent.futures.as_completed(futures):
            i, ans = f.result()
            results[i] = ans

    return results

# --- Entry Point ---
if __name__ == "__main__":
    try:
        df_test = pd.read_csv('test.csv')
        logging.info(f"Loaded {len(df_test)} queries")
        predictions = run(df_test)
        df_sub = pd.read_csv('submission.csv')
        df_sub['answer'] = predictions
        df_sub.to_csv('my_submission_typhoon_v2.1.csv', index=False)
        logging.info("Submission saved as my_submission_typhoon_v2.1.csv")
        logging.info(f"Prediction distribution: {collections.Counter(predictions)}")
    except Exception as e:
        logging.error(f"Main pipeline failed: {e}")
