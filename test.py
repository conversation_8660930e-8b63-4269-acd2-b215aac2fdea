import re
import collections
import pandas as pd
import torch
from tqdm.notebook import tqdm
from openai import OpenAI
import multiprocessing as mp
from concurrent.futures import ThreadPoolExecutor, as_completed
import asyncio
import aiohttp
import json
import time
from functools import lru_cache

# Check if CUDA is available and set device
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"Using device: {device}")

# 1. Load test file
test_df = pd.read_csv("test.csv")

# 2. Initialize OpenAI-compatible client
client = OpenAI(
    api_key="sk-ArwixD6prvk3kCwlHbRb3Mjv9wMbByoaVrOXXQDnKnQqJBCV",  
    base_url="https://api.opentyphoon.ai/v1"
)

# 3. Enhanced Constants
N_SAMPLES = 7  # Increased odd number for better majority voting
MAX_WORKERS = 8  # Parallel processing
BATCH_SIZE = 5  # Process queries in batches

# Improved prompt with better structure and financial reasoning
PROMPT_SYS = """You are a world-class financial analyst and quantitative researcher with expertise in:
- Financial markets, instruments, and derivatives
- Economic theory and monetary policy
- Corporate finance and valuation
- Risk management and portfolio optimization
- Statistical analysis and financial modeling

ANALYSIS FRAMEWORK:
1. COMPREHENSION: Identify the core financial concept, key variables, and question type
2. CONTEXT: Consider market conditions, economic principles, and relevant financial theories
3. CALCULATION: Apply appropriate formulas, ratios, or quantitative methods
4. LOGIC: Use step-by-step reasoning, considering cause-and-effect relationships
5. VALIDATION: Cross-check your analysis against financial best practices
6. DECISION: Select the most accurate answer with high confidence

ANSWER FORMAT:
- Provide concise but thorough analysis
- Show key calculations or reasoning steps
- State your confidence level (High/Medium/Low)
- End with: "FINAL ANSWER: [A/B/C/D/Rise/Fall]"

QUALITY STANDARDS:
- Prioritize accuracy over speed
- Consider multiple scenarios when uncertain
- Apply conservative assumptions when data is ambiguous
- Use established financial principles and formulas
"""

# 4. Optimized choice extraction with precompiled regex
class ChoiceExtractor:
    def __init__(self):
        self.final_pattern = re.compile(r"FINAL ANSWER:\s*([ABCD]|Rise|Fall)", re.IGNORECASE)
        self.fallback_pattern = re.compile(r"(?:answer|choice|select|option).*?([ABCD]|Rise|Fall)", re.IGNORECASE)
        self.last_choice_pattern = re.compile(r"\b([ABCD]|Rise|Fall)\b(?!.*\b(?:[ABCD]|Rise|Fall)\b)", re.IGNORECASE)
    
    def extract(self, text: str) -> str:
        """Extract choice with multiple fallback strategies"""
        if not text:
            return None
            
        # Primary: Look for FINAL ANSWER
        match = self.final_pattern.search(text)
        if match:
            return self._normalize_choice(match.group(1))
        
        # Secondary: Look for answer context
        match = self.fallback_pattern.search(text)
        if match:
            return self._normalize_choice(match.group(1))
        
        # Tertiary: Last occurrence
        match = self.last_choice_pattern.search(text)
        if match:
            return self._normalize_choice(match.group(1))
        
        return None
    
    def _normalize_choice(self, choice: str) -> str:
        """Normalize choice format"""
        choice = choice.strip()
        if choice.lower() in ['rise', 'fall']:
            return choice.capitalize()
        return choice.upper()

extractor = ChoiceExtractor()

# 5. Enhanced API calling with retry logic and better error handling
def call_with_retry(prompt_sys: str, prompt_usr: str, temp=0.7, max_retries=3) -> str:
    """API call with exponential backoff retry"""
    for attempt in range(max_retries):
        try:
            resp = client.chat.completions.create(
                model="typhoon-v2.1-12b-instruct",
                messages=[
                    {"role": "system", "content": prompt_sys},
                    {"role": "user", "content": prompt_usr},
                ],
                temperature=temp,
                top_p=0.85,  # Slightly higher for better diversity
                max_tokens=12000,  # Reduced for faster processing
                extra_body={
                    "top_k": 25,
                    "min_p": 0.02,  # Better quality control
                    "chat_template_kwargs": {"enable_thinking": False},
                },
            )
            return resp.choices[0].message.content
        except Exception as e:
            wait_time = (2 ** attempt) + (0.1 * attempt)  # Exponential backoff
            print(f"API call failed (attempt {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                time.sleep(wait_time)
            else:
                raise e

# 6. Intelligent sampling strategy
def adaptive_sampling(prompt_sys: str, prompt_usr: str, max_samples=N_SAMPLES, confidence_threshold=0.6):
    """Adaptive sampling that can stop early if confidence is high"""
    predictions = []
    temperatures = [0.3, 0.5, 0.7, 0.8, 0.9, 0.6, 0.4]  # Strategic temperature distribution
    
    for i in range(max_samples):
        try:
            response = call_with_retry(prompt_sys, prompt_usr, temp=temperatures[i % len(temperatures)])
            choice = extractor.extract(response)
            
            if choice:
                predictions.append(choice)
                
                # Early stopping if we have strong consensus
                if len(predictions) >= 3:
                    counter = collections.Counter(predictions)
                    most_common_count = counter.most_common(1)[0][1]
                    confidence = most_common_count / len(predictions)
                    
                    if confidence >= confidence_threshold and len(predictions) >= 5:
                        print(f"   Early stopping at sample {i+1} (confidence: {confidence:.2f})")
                        break
                        
                print(f"   Sample {i+1}: {choice}")
            else:
                print(f"   Sample {i+1}: ❌ No valid choice extracted")
                
        except Exception as e:
            print(f"   Sample {i+1}: ❌ Error: {e}")
    
    if not predictions:
        return None, []
    
    # Enhanced voting with confidence scoring
    counter = collections.Counter(predictions)
    winner, winner_count = counter.most_common(1)[0]
    confidence = winner_count / len(predictions)
    
    print(f" ✔️ Final choice: {winner} (confidence: {confidence:.2f}, votes: {winner_count}/{len(predictions)})")
    
    return winner, predictions

# 7. Parallel processing for batch operations
def process_query_batch(batch_data):
    """Process a batch of queries in parallel"""
    batch_results = []
    
    with ThreadPoolExecutor(max_workers=min(MAX_WORKERS, len(batch_data))) as executor:
        future_to_query = {
            executor.submit(adaptive_sampling, PROMPT_SYS, row['query']): (row['id'], i)
            for i, row in batch_data
        }
        
        for future in as_completed(future_to_query):
            query_id, original_index = future_to_query[future]
            try:
                result, samples = future.result()
                batch_results.append((original_index, query_id, result))
            except Exception as e:
                print(f"❌ Error processing query {query_id}: {e}")
                batch_results.append((original_index, query_id, None))
    
    return batch_results

# 8. Quality validation
def validate_prediction(prediction: str, query: str) -> bool:
    """Basic validation of prediction quality"""
    if not prediction:
        return False
    
    valid_choices = {'A', 'B', 'C', 'D', 'Rise', 'Fall'}
    return prediction in valid_choices

# 9. Main execution with progress tracking
def run_predictions():
    predictions = [None] * len(test_df)
    failed_queries = []
    
    # Process in batches for better resource management
    total_batches = (len(test_df) + BATCH_SIZE - 1) // BATCH_SIZE
    
    for batch_idx in range(total_batches):
        start_idx = batch_idx * BATCH_SIZE
        end_idx = min((batch_idx + 1) * BATCH_SIZE, len(test_df))
        
        print(f"\n📊 Processing batch {batch_idx + 1}/{total_batches} (queries {start_idx + 1}-{end_idx})")
        
        batch_data = [(i, test_df.iloc[i]) for i in range(start_idx, end_idx)]
        batch_results = process_query_batch(batch_data)
        
        # Store results
        for original_index, query_id, result in batch_results:
            if validate_prediction(result, test_df.iloc[original_index]['query']):
                predictions[original_index] = result
            else:
                print(f"⚠️ Invalid prediction for query {query_id}: {result}")
                failed_queries.append((original_index, query_id))
    
    # Handle failed queries with fallback strategy
    if failed_queries:
        print(f"\n🔄 Retrying {len(failed_queries)} failed queries with simpler approach...")
        for original_index, query_id in failed_queries:
            try:
                query = test_df.iloc[original_index]['query']
                response = call_with_retry(PROMPT_SYS, query, temp=0.5)
                fallback_choice = extractor.extract(response)
                if validate_prediction(fallback_choice, query):
                    predictions[original_index] = fallback_choice
                else:
                    predictions[original_index] = 'A'  # Conservative fallback
            except Exception as e:
                print(f"❌ Final fallback failed for {query_id}: {e}")
                predictions[original_index] = 'A'  # Conservative fallback
    
    return predictions

# 10. Execute and save results
if __name__ == "__main__":
    print("🚀 Starting optimized financial analysis prediction pipeline...")
    start_time = time.time()
    
    predictions = run_predictions()
    
    # Generate submission
    submission = pd.read_csv("submission.csv")
    submission["answer"] = predictions
    submission.to_csv("my_submission_optimized.csv", index=False)
    
    end_time = time.time()
    execution_time = end_time - start_time
    
    print(f"\n✅ Pipeline completed successfully!")
    print(f"⏱️ Total execution time: {execution_time:.2f} seconds")
    print(f"📊 Average time per query: {execution_time/len(test_df):.2f} seconds")
    print(f"💾 Results saved to 'my_submission_optimized.csv'")
    
    # Quality report
    valid_predictions = sum(1 for p in predictions if p is not None)
    print(f"📈 Quality report: {valid_predictions}/{len(test_df)} valid predictions ({valid_predictions/len(test_df)*100:.1f}%)")